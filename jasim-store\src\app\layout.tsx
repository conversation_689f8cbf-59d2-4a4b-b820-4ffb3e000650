import type { Metadata } from 'next';
import { Cairo } from 'next/font/google';
import './globals.css';
import { storeSettings } from '@/data/store-settings';

const cairo = Cairo({
  subsets: ['arabic', 'latin'],
  display: 'swap',
  variable: '--font-cairo',
});

export const metadata: Metadata = {
  title: {
    default: storeSettings.storeName,
    template: `%s | ${storeSettings.storeName}`
  },
  description: storeSettings.description,
  keywords: ['متجر إلكتروني', 'تسوق أونلاين', 'منتجات عالية الجودة', 'جاسم الدليمي'],
  authors: [{ name: storeSettings.storeName }],
  creator: storeSettings.storeName,
  publisher: storeSettings.storeName,
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://jasimstore.com'),
  alternates: {
    canonical: '/',
    languages: {
      'ar': '/ar',
      'en': '/en',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'ar_IQ',
    url: 'https://jasimstore.com',
    title: storeSettings.storeName,
    description: storeSettings.description,
    siteName: storeSettings.storeName,
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: storeSettings.storeName,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: storeSettings.storeName,
    description: storeSettings.description,
    images: ['/images/twitter-image.jpg'],
    creator: '@jasimstore',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'google-verification-code',
    yandex: 'yandex-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="ar" dir="rtl" className={cairo.variable}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#1e40af" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body className={`${cairo.className} antialiased`}>
        {children}
      </body>
    </html>
  );
}

#!/bin/bash

echo "========================================"
echo "       متجر جاسم الدليمي الإلكتروني"
echo "========================================"
echo

echo "تحقق من وجود Node.js..."
if ! command -v node &> /dev/null; then
    echo "خطأ: Node.js غير مثبت على النظام"
    echo "يرجى تثبيت Node.js من: https://nodejs.org"
    exit 1
fi

echo "Node.js موجود ✓"
echo

echo "تثبيت التبعيات..."
npm install
if [ $? -ne 0 ]; then
    echo "خطأ في تثبيت التبعيات"
    exit 1
fi

echo "التبعيات تم تثبيتها بنجاح ✓"
echo

echo "بدء تشغيل المتجر..."
echo "المتجر سيكون متاح على: http://localhost:3000"
echo
echo "اضغط Ctrl+C لإيقاف الخادم"
echo

npm run dev

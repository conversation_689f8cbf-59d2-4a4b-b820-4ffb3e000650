'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Search, ShoppingCart, Menu, X, User, Heart } from 'lucide-react';
import Button from '@/components/ui/Button';
import { useCart } from '@/utils/cart-context';
import { storeSettings } from '@/data/store-settings';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { getTotalItems } = useCart();

  const navigation = [
    { name: 'الرئيسية', href: '/' },
    { name: 'المنتجات', href: '/products' },
    { name: 'الفئات', href: '/categories' },
    { name: 'العروض', href: '/offers' },
    { name: 'من نحن', href: '/about' },
    { name: 'تواصل معنا', href: '/contact' }
  ];

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // التوجه إلى صفحة البحث
      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;
    }
  };

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50">
      {/* شريط علوي للمعلومات */}
      <div className="bg-primary-600 text-white py-2">
        <div className="container-custom">
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center space-x-4 space-x-reverse">
              <span>📞 {storeSettings.contactInfo.phone}</span>
              <span>✉️ {storeSettings.contactInfo.email}</span>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <span>🚚 شحن مجاني للطلبات أكثر من {storeSettings.shipping.freeShippingThreshold.toLocaleString()} د.ع</span>
            </div>
          </div>
        </div>
      </div>

      {/* الرأس الرئيسي */}
      <div className="container-custom py-4">
        <div className="flex items-center justify-between">
          {/* الشعار */}
          <Link href="/" className="flex items-center space-x-2 space-x-reverse">
            <div className="w-10 h-10 bg-gradient-to-r from-primary-600 to-gold-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">ج</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">{storeSettings.storeName}</h1>
              <p className="text-sm text-gray-500">متجرك الإلكتروني المتميز</p>
            </div>
          </Link>

          {/* شريط البحث */}
          <div className="hidden md:flex flex-1 max-w-lg mx-8">
            <form onSubmit={handleSearch} className="w-full">
              <div className="relative">
                <input
                  type="text"
                  placeholder="ابحث عن المنتجات..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary-600"
                >
                  <Search className="w-5 h-5" />
                </button>
              </div>
            </form>
          </div>

          {/* أيقونات الإجراءات */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {/* المفضلة */}
            <button className="p-2 text-gray-600 hover:text-primary-600 transition-colors">
              <Heart className="w-6 h-6" />
            </button>

            {/* الحساب */}
            <button className="p-2 text-gray-600 hover:text-primary-600 transition-colors">
              <User className="w-6 h-6" />
            </button>

            {/* سلة الشراء */}
            <Link href="/cart" className="relative p-2 text-gray-600 hover:text-primary-600 transition-colors">
              <ShoppingCart className="w-6 h-6" />
              {getTotalItems() > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {getTotalItems()}
                </span>
              )}
            </Link>

            {/* زر القائمة للموبايل */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 text-gray-600 hover:text-primary-600"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* شريط التنقل */}
      <nav className="bg-gray-50 border-t">
        <div className="container-custom">
          {/* التنقل للشاشات الكبيرة */}
          <div className="hidden md:flex items-center justify-center space-x-8 space-x-reverse py-3">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-gray-700 hover:text-primary-600 font-medium transition-colors"
              >
                {item.name}
              </Link>
            ))}
          </div>

          {/* القائمة المنسدلة للموبايل */}
          {isMenuOpen && (
            <div className="md:hidden py-4 border-t">
              {/* شريط البحث للموبايل */}
              <div className="mb-4">
                <form onSubmit={handleSearch}>
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="ابحث عن المنتجات..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                    <button
                      type="submit"
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400"
                    >
                      <Search className="w-5 h-5" />
                    </button>
                  </div>
                </form>
              </div>

              {/* روابط التنقل */}
              <div className="space-y-2">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="block py-2 text-gray-700 hover:text-primary-600 font-medium transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      </nav>
    </header>
  );
};

export default Header;

{"name": "jasim-store", "version": "1.0.0", "description": "متجر جاسم الدليمي الإلكتروني - متجر إلكتروني احترافي مبني بـ Next.js", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write .", "clean": "rm -rf .next out"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "lucide-react": "^0.300.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9"}, "keywords": ["ecommerce", "nextjs", "tailwindcss", "typescript", "arabic", "rtl", "online-store", "متجر-إل<PERSON>تروني"], "author": {"name": "جاسم الدليمي", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jasim-store/jasim-store.git"}, "bugs": {"url": "https://github.com/jasim-store/jasim-store/issues"}, "homepage": "https://jasimstore.com"}
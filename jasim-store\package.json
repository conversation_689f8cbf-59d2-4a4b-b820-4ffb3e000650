{"name": "jasim-store", "version": "1.0.0", "description": "متجر جاسم الدليمي الإلكتروني", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^5.0.0", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "lucide-react": "^0.292.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0"}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-next": "14.0.0"}}
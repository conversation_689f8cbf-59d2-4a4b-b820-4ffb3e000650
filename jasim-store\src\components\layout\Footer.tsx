import React from 'react';
import Link from 'next/link';
import { Facebook, Instagram, Twitter, Phone, Mail, MapPin } from 'lucide-react';
import { storeSettings } from '@/data/store-settings';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: 'روابط سريعة',
      links: [
        { name: 'الرئيسية', href: '/' },
        { name: 'المنتجات', href: '/products' },
        { name: 'الفئات', href: '/categories' },
        { name: 'العروض', href: '/offers' }
      ]
    },
    {
      title: 'خدمة العملاء',
      links: [
        { name: 'تواصل معنا', href: '/contact' },
        { name: 'الأسئلة الشائعة', href: '/faq' },
        { name: 'سياسة الإرجاع', href: '/return-policy' },
        { name: 'الشحن والتوصيل', href: '/shipping' }
      ]
    },
    {
      title: 'معلومات قانونية',
      links: [
        { name: 'من نحن', href: '/about' },
        { name: 'سياسة الخصوصية', href: '/privacy' },
        { name: 'شروط الاستخدام', href: '/terms' },
        { name: 'سياسة الكوكيز', href: '/cookies' }
      ]
    }
  ];

  return (
    <footer className="bg-gray-900 text-white">
      {/* القسم الرئيسي */}
      <div className="container-custom py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* معلومات المتجر */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 space-x-reverse mb-4">
              <div className="w-10 h-10 bg-gradient-to-r from-primary-600 to-gold-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">ج</span>
              </div>
              <div>
                <h3 className="text-xl font-bold">{storeSettings.storeName}</h3>
                <p className="text-gray-400 text-sm">متجرك الإلكتروني المتميز</p>
              </div>
            </div>
            
            <p className="text-gray-300 mb-6 leading-relaxed">
              {storeSettings.description}
            </p>

            {/* وسائل التواصل الاجتماعي */}
            <div className="flex space-x-4 space-x-reverse">
              {storeSettings.contactInfo.socialMedia.facebook && (
                <a
                  href={storeSettings.contactInfo.socialMedia.facebook}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors"
                >
                  <Facebook className="w-5 h-5" />
                </a>
              )}
              
              {storeSettings.contactInfo.socialMedia.instagram && (
                <a
                  href={storeSettings.contactInfo.socialMedia.instagram}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-pink-600 rounded-lg flex items-center justify-center hover:bg-pink-700 transition-colors"
                >
                  <Instagram className="w-5 h-5" />
                </a>
              )}
              
              {storeSettings.contactInfo.socialMedia.twitter && (
                <a
                  href={storeSettings.contactInfo.socialMedia.twitter}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-blue-400 rounded-lg flex items-center justify-center hover:bg-blue-500 transition-colors"
                >
                  <Twitter className="w-5 h-5" />
                </a>
              )}
            </div>
          </div>

          {/* أقسام الروابط */}
          {footerSections.map((section, index) => (
            <div key={index}>
              <h4 className="text-lg font-semibold mb-4 text-gold-400">{section.title}</h4>
              <ul className="space-y-2">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* معلومات الاتصال */}
        <div className="mt-12 pt-8 border-t border-gray-700">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                <Phone className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-400">اتصل بنا</p>
                <p className="font-medium">{storeSettings.contactInfo.phone}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                <Mail className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-400">راسلنا</p>
                <p className="font-medium">{storeSettings.contactInfo.email}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                <MapPin className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-400">عنواننا</p>
                <p className="font-medium">
                  {storeSettings.contactInfo.address.city}, {storeSettings.contactInfo.address.country}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* شريط حقوق الطبع */}
      <div className="bg-gray-800 py-4">
        <div className="container-custom">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © {currentYear} {storeSettings.storeName}. جميع الحقوق محفوظة.
            </p>
            <div className="flex items-center space-x-4 space-x-reverse mt-2 md:mt-0">
              <span className="text-gray-400 text-sm">مدعوم بـ</span>
              <span className="text-gold-400 font-medium">Next.js & Tailwind CSS</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

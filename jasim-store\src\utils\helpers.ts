// دوال مساعدة للمتجر

import { Product, CartItem } from '@/types';

// تنسيق السعر
export const formatPrice = (price: number, currency: string = 'IQD'): string => {
  return new Intl.NumberFormat('ar-IQ', {
    style: 'currency',
    currency: currency === 'IQD' ? 'USD' : currency, // استخدام USD كبديل للدينار العراقي
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price).replace('US$', 'د.ع');
};

// تنسيق التاريخ
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('ar-IQ', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date);
};

// حساب نسبة الخصم
export const calculateDiscountPercentage = (originalPrice: number, currentPrice: number): number => {
  return Math.round(((originalPrice - currentPrice) / originalPrice) * 100);
};

// التحقق من وجود خصم
export const hasDiscount = (product: Product): boolean => {
  return product.originalPrice ? product.originalPrice > product.price : false;
};

// حساب السعر بعد الخصم
export const getDiscountedPrice = (product: Product): number => {
  if (product.discount && product.originalPrice) {
    return product.originalPrice - (product.originalPrice * product.discount / 100);
  }
  return product.price;
};

// تقصير النص
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// توليد معرف فريد
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// التحقق من صحة البريد الإلكتروني
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// التحقق من صحة رقم الهاتف العراقي
export const isValidIraqiPhone = (phone: string): boolean => {
  const phoneRegex = /^(\+964|0)?7[0-9]{9}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};

// حساب إجمالي سلة الشراء
export const calculateCartTotal = (items: CartItem[]): number => {
  return items.reduce((total, item) => {
    return total + (item.product.price * item.quantity);
  }, 0);
};

// حساب عدد العناصر في السلة
export const calculateCartItemsCount = (items: CartItem[]): number => {
  return items.reduce((total, item) => total + item.quantity, 0);
};

// البحث في المنتجات
export const searchProducts = (products: Product[], query: string): Product[] => {
  const searchTerm = query.toLowerCase().trim();
  if (!searchTerm) return products;

  return products.filter(product => 
    product.name.toLowerCase().includes(searchTerm) ||
    product.description.toLowerCase().includes(searchTerm) ||
    product.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
    (product.nameEn && product.nameEn.toLowerCase().includes(searchTerm)) ||
    (product.descriptionEn && product.descriptionEn.toLowerCase().includes(searchTerm))
  );
};

// ترتيب المنتجات
export const sortProducts = (
  products: Product[], 
  sortBy: 'name' | 'price' | 'rating' | 'newest',
  order: 'asc' | 'desc' = 'asc'
): Product[] => {
  const sorted = [...products].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return order === 'asc' 
          ? a.name.localeCompare(b.name, 'ar')
          : b.name.localeCompare(a.name, 'ar');
      case 'price':
        return order === 'asc' ? a.price - b.price : b.price - a.price;
      case 'rating':
        return order === 'asc' ? a.rating - b.rating : b.rating - a.rating;
      case 'newest':
        return order === 'asc' 
          ? new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          : new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      default:
        return 0;
    }
  });
  
  return sorted;
};

// فلترة المنتجات حسب الفئة
export const filterProductsByCategory = (products: Product[], categoryId: string): Product[] => {
  return products.filter(product => product.category === categoryId);
};

// فلترة المنتجات حسب النطاق السعري
export const filterProductsByPriceRange = (
  products: Product[], 
  minPrice: number, 
  maxPrice: number
): Product[] => {
  return products.filter(product => 
    product.price >= minPrice && product.price <= maxPrice
  );
};

// فلترة المنتجات المتوفرة فقط
export const filterInStockProducts = (products: Product[]): Product[] => {
  return products.filter(product => product.inStock && product.stockQuantity > 0);
};

// تحويل الرقم إلى نص عربي
export const numberToArabicText = (num: number): string => {
  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  return num.toString().replace(/[0-9]/g, (digit) => arabicNumbers[parseInt(digit)]);
};

// تحويل النص العربي إلى رقم
export const arabicTextToNumber = (text: string): string => {
  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  return text.replace(/[٠-٩]/g, (digit) => arabicNumbers.indexOf(digit).toString());
};

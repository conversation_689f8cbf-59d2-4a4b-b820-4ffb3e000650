'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Minus, Plus, Trash2, ShoppingBag, ArrowLeft } from 'lucide-react';
import Layout from '@/components/layout/Layout';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { useCart } from '@/utils/cart-context';
import { formatPrice } from '@/utils/helpers';
import { storeSettings } from '@/data/store-settings';

const CartPage: React.FC = () => {
  const { items, updateQuantity, removeFromCart, getTotalPrice, getTotalItems } = useCart();

  const shippingCost = getTotalPrice() >= storeSettings.shipping.freeShippingThreshold 
    ? 0 
    : storeSettings.shipping.standardShippingCost;

  const totalWithShipping = getTotalPrice() + shippingCost;

  if (items.length === 0) {
    return (
      <Layout>
        <div className="container-custom py-16">
          <div className="text-center max-w-md mx-auto">
            <div className="text-gray-400 mb-6">
              <ShoppingBag className="w-24 h-24 mx-auto" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">سلة التسوق فارغة</h1>
            <p className="text-gray-600 mb-8">
              لم تقم بإضافة أي منتجات إلى سلة التسوق بعد
            </p>
            <Link href="/products">
              <Button size="lg">
                تصفح المنتجات
                <ArrowLeft className="w-5 h-5 mr-2" />
              </Button>
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* رأس الصفحة */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">سلة التسوق</h1>
          <p className="text-gray-600">
            لديك {getTotalItems()} {getTotalItems() === 1 ? 'منتج' : 'منتجات'} في السلة
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* قائمة المنتجات */}
          <div className="lg:col-span-2">
            <Card>
              <div className="divide-y divide-gray-200">
                {items.map((item) => (
                  <div key={`${item.productId}-${item.selectedSize}-${item.selectedColor}`} className="p-6">
                    <div className="flex items-start gap-4">
                      {/* صورة المنتج */}
                      <div className="relative w-20 h-20 flex-shrink-0">
                        <Image
                          src={item.product.images[0] || '/images/placeholder-product.jpg'}
                          alt={item.product.name}
                          fill
                          className="object-cover rounded-lg"
                        />
                      </div>

                      {/* معلومات المنتج */}
                      <div className="flex-1 min-w-0">
                        <Link 
                          href={`/products/${item.product.id}`}
                          className="text-lg font-medium text-gray-900 hover:text-primary-600 transition-colors"
                        >
                          {item.product.name}
                        </Link>
                        
                        <div className="mt-1 space-y-1">
                          {item.selectedSize && (
                            <p className="text-sm text-gray-500">
                              الحجم: <span className="font-medium">{item.selectedSize}</span>
                            </p>
                          )}
                          {item.selectedColor && (
                            <p className="text-sm text-gray-500">
                              اللون: <span className="font-medium">{item.selectedColor}</span>
                            </p>
                          )}
                        </div>

                        <div className="mt-2 flex items-center justify-between">
                          {/* السعر */}
                          <div className="text-lg font-bold text-primary-600">
                            {formatPrice(item.product.price)}
                          </div>

                          {/* التحكم في الكمية */}
                          <div className="flex items-center gap-3">
                            <div className="flex items-center border border-gray-300 rounded-lg">
                              <button
                                onClick={() => updateQuantity(item.productId, item.quantity - 1)}
                                className="p-2 hover:bg-gray-50 transition-colors"
                                disabled={item.quantity <= 1}
                              >
                                <Minus className="w-4 h-4" />
                              </button>
                              
                              <span className="px-4 py-2 font-medium min-w-[3rem] text-center">
                                {item.quantity}
                              </span>
                              
                              <button
                                onClick={() => updateQuantity(item.productId, item.quantity + 1)}
                                className="p-2 hover:bg-gray-50 transition-colors"
                                disabled={item.quantity >= item.product.stockQuantity}
                              >
                                <Plus className="w-4 h-4" />
                              </button>
                            </div>

                            {/* زر الحذف */}
                            <button
                              onClick={() => removeFromCart(item.productId)}
                              className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                              title="حذف من السلة"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>

                        {/* إجمالي سعر المنتج */}
                        <div className="mt-2 text-right">
                          <span className="text-sm text-gray-500">الإجمالي: </span>
                          <span className="font-bold text-gray-900">
                            {formatPrice(item.product.price * item.quantity)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* أزرار الإجراءات */}
            <div className="mt-6 flex flex-col sm:flex-row gap-4">
              <Link href="/products" className="flex-1">
                <Button variant="outline" fullWidth>
                  متابعة التسوق
                  <ArrowLeft className="w-4 h-4 mr-2" />
                </Button>
              </Link>
            </div>
          </div>

          {/* ملخص الطلب */}
          <div className="lg:col-span-1">
            <Card className="sticky top-24">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">ملخص الطلب</h3>
              
              <div className="space-y-3 mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-600">المجموع الفرعي:</span>
                  <span className="font-medium">{formatPrice(getTotalPrice())}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">الشحن:</span>
                  <span className="font-medium">
                    {shippingCost === 0 ? (
                      <span className="text-green-600">مجاني</span>
                    ) : (
                      formatPrice(shippingCost)
                    )}
                  </span>
                </div>
                
                {shippingCost === 0 && getTotalPrice() >= storeSettings.shipping.freeShippingThreshold && (
                  <div className="text-sm text-green-600 bg-green-50 p-2 rounded">
                    🎉 تهانينا! حصلت على شحن مجاني
                  </div>
                )}
                
                {shippingCost > 0 && (
                  <div className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
                    أضف {formatPrice(storeSettings.shipping.freeShippingThreshold - getTotalPrice())} للحصول على شحن مجاني
                  </div>
                )}
                
                <hr className="border-gray-200" />
                
                <div className="flex justify-between text-lg font-bold">
                  <span>الإجمالي:</span>
                  <span className="text-primary-600">{formatPrice(totalWithShipping)}</span>
                </div>
              </div>

              <Link href="/checkout">
                <Button size="lg" fullWidth>
                  إتمام الطلب
                  <ArrowLeft className="w-5 h-5 mr-2" />
                </Button>
              </Link>

              {/* معلومات إضافية */}
              <div className="mt-6 space-y-3 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <span>🔒</span>
                  <span>دفع آمن ومحمي</span>
                </div>
                <div className="flex items-center gap-2">
                  <span>🚚</span>
                  <span>توصيل سريع</span>
                </div>
                <div className="flex items-center gap-2">
                  <span>↩️</span>
                  <span>إرجاع مجاني خلال 30 يوم</span>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default CartPage;

'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowLeft, Star, Truck, Shield, Headphones, RotateCcw } from 'lucide-react';
import Layout from '@/components/layout/Layout';
import ProductCard from '@/components/product/ProductCard';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { featuredProducts, newProducts, saleProducts, categories } from '@/data/products';
import { storeSettings } from '@/data/store-settings';

const HomePage: React.FC = () => {
  const features = [
    {
      icon: <Truck className="w-8 h-8" />,
      title: 'شحن سريع',
      description: 'توصيل مجاني للطلبات أكثر من 100,000 د.ع'
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: 'دفع آمن',
      description: 'حماية كاملة لبياناتك المالية'
    },
    {
      icon: <Headphones className="w-8 h-8" />,
      title: 'دعم 24/7',
      description: 'خدمة عملاء متاحة على مدار الساعة'
    },
    {
      icon: <RotateCcw className="w-8 h-8" />,
      title: 'إرجاع مجاني',
      description: 'إمكانية الإرجاع خلال 30 يوم'
    }
  ];

  return (
    <Layout>
      {/* البانر الرئيسي */}
      <section className="relative bg-gradient-to-l from-primary-600 to-primary-800 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative container-custom py-20 lg:py-32">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-right">
              <h1 className="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
                مرحباً بك في
                <span className="block text-gold-400">{storeSettings.storeName}</span>
              </h1>
              <p className="text-xl lg:text-2xl mb-8 text-gray-200 leading-relaxed">
                اكتشف أفضل المنتجات بجودة عالية وأسعار منافسة
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Button size="lg" variant="secondary">
                  تسوق الآن
                  <ArrowLeft className="w-5 h-5 mr-2" />
                </Button>
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600">
                  استكشف الفئات
                </Button>
              </div>
            </div>

            <div className="relative">
              <div className="relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl">
                <Image
                  src="https://via.placeholder.com/600x500/1e40af/ffffff?text=متجر+جاسم+الدليمي"
                  alt="متجر جاسم الدليمي"
                  fill
                  className="object-cover"
                  priority
                />
              </div>

              {/* عناصر ديكورية */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gold-400 rounded-full opacity-20 animate-pulse"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-white rounded-full opacity-10 animate-bounce"></div>
            </div>
          </div>
        </div>
      </section>

      {/* الفئات الرئيسية */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              تسوق حسب الفئة
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              اكتشف مجموعتنا المتنوعة من المنتجات المصنفة بعناية لتسهيل تجربة التسوق
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {categories.map((category) => (
              <Link key={category.id} href={`/categories/${category.id}`}>
                <Card hover className="text-center group">
                  <div className="relative h-48 mb-4 overflow-hidden rounded-lg">
                    <Image
                      src={category.image || '/images/placeholder-category.jpg'}
                      alt={category.name}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                  </div>
                  <h3 className="font-semibold text-lg text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                    {category.name}
                  </h3>
                  <p className="text-sm text-gray-500 mb-3">{category.description}</p>
                  <span className="text-primary-600 font-medium">
                    {category.productCount} منتج
                  </span>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* المنتجات المميزة */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">المنتجات المميزة</h2>
              <p className="text-gray-600">أفضل منتجاتنا المختارة بعناية</p>
            </div>
            <Link href="/products?featured=true">
              <Button variant="outline">
                عرض الكل
                <ArrowLeft className="w-4 h-4 mr-2" />
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredProducts.slice(0, 8).map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </div>
      </section>

      {/* المنتجات الجديدة */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">وصل حديثاً</h2>
              <p className="text-gray-600">أحدث المنتجات في متجرنا</p>
            </div>
            <Link href="/products?new=true">
              <Button variant="outline">
                عرض الكل
                <ArrowLeft className="w-4 h-4 mr-2" />
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {newProducts.slice(0, 4).map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </div>
      </section>

      {/* العروض الخاصة */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">عروض خاصة</h2>
              <p className="text-gray-600">خصومات مذهلة لفترة محدودة</p>
            </div>
            <Link href="/products?sale=true">
              <Button variant="secondary">
                عرض الكل
                <ArrowLeft className="w-4 h-4 mr-2" />
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {saleProducts.slice(0, 4).map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </div>
      </section>

      {/* مميزات المتجر */}
      <section className="section-padding bg-primary-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">لماذا تختارنا؟</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              نقدم لك أفضل تجربة تسوق مع خدمات متميزة وجودة عالية
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default HomePage;

# 🚀 دليل البدء السريع - متجر جاسم الدليمي

## ⚡ تشغيل المتجر بسرعة

### للمستخدمين المبتدئين:

#### على Windows:
1. انقر نقرًا مزدوجًا على ملف `start.bat`
2. انتظر حتى يكتمل التثبيت والتشغيل
3. افتح المتصفح على `http://localhost:3000`

#### على Mac/Linux:
1. افتح Terminal في مجلد المشروع
2. اكتب: `chmod +x start.sh && ./start.sh`
3. افتح المتصفح على `http://localhost:3000`

### للمطورين:

```bash
# تثبيت التبعيات
npm install

# تشغيل المشروع
npm run dev

# بناء المشروع للإنتاج
npm run build

# تشغيل النسخة المبنية
npm start
```

## 📱 معاينة المتجر

بعد التشغيل، ستجد:

- **الصفحة الرئيسية**: `/` - عرض المنتجات المميزة
- **جميع المنتجات**: `/products` - تصفح وفلترة المنتجات  
- **سلة الشراء**: `/cart` - إدارة المشتريات
- **الفئات**: `/categories` - تصفح حسب الفئة

## 🎨 المميزات المتاحة

✅ **تم تطويرها:**
- واجهة مستخدم عربية كاملة مع RTL
- تصميم متجاوب لجميع الشاشات
- سلة شراء تفاعلية مع Local Storage
- فلترة وبحث في المنتجات
- نظام تقييم المنتجات
- حساب الشحن والخصومات

🔄 **قيد التطوير:**
- صفحة إتمام الطلب
- لوحة تحكم إدارية
- نظام المستخدمين
- دفع إلكتروني حقيقي

## 🛠️ التخصيص السريع

### تغيير اسم المتجر:
عدّل ملف `src/data/store-settings.ts`:
```typescript
storeName: 'اسم متجرك الجديد'
```

### إضافة منتجات:
عدّل ملف `src/data/products.ts` وأضف منتجاتك.

### تغيير الألوان:
عدّل ملف `tailwind.config.js` في قسم الألوان.

## 🆘 حل المشاكل الشائعة

**المشكلة**: `npm: command not found`
**الحل**: ثبت Node.js من https://nodejs.org

**المشكلة**: خطأ في المنافذ
**الحل**: تأكد أن المنفذ 3000 غير مستخدم

**المشكلة**: الصور لا تظهر
**الحل**: تأكد من اتصال الإنترنت (نستخدم placeholder images)

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف `README.md` للتفاصيل الكاملة
2. تأكد من تثبيت Node.js الإصدار 18+
3. جرب حذف مجلد `node_modules` وإعادة تشغيل `npm install`

---

**نصيحة**: احفظ هذا الملف كمرجع سريع! 📌

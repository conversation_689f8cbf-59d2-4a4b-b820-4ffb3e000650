@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* إعدادات عامة للغة العربية */
:root {
  --primary-color: #1e40af;
  --secondary-color: #f59e0b;
  --accent-color: #10b981;
  --text-color: #1f2937;
  --bg-color: #ffffff;
  --border-color: #e5e7eb;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  direction: rtl;
  font-family: 'Cairo', 'Tajawal', sans-serif;
}

body {
  color: var(--text-color);
  background: var(--bg-color);
  line-height: 1.6;
  font-size: 16px;
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #1e3a8a;
}

/* أنماط مخصصة للمكونات */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-gold-500 hover:bg-gold-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:ring-offset-2;
  }

  .btn-outline {
    @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
  }

  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden;
  }

  .card-hover {
    @apply card hover:shadow-lg hover:scale-105 transition-all duration-300;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-padding {
    @apply py-12 lg:py-16;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-gold-500 bg-clip-text text-transparent;
  }

  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }

  .badge-error {
    @apply badge bg-red-100 text-red-800;
  }

  .badge-info {
    @apply badge bg-blue-100 text-blue-800;
  }
}

/* أنماط للرسوم المتحركة */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* أنماط للطباعة */
@media print {
  .no-print {
    display: none !important;
  }
}

/* أنماط للشاشات الصغيرة */
@media (max-width: 640px) {
  .container-custom {
    @apply px-3;
  }
  
  .section-padding {
    @apply py-8;
  }
}

/* تحسينات للوصولية */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* أنماط للتركيز */
.focus-visible:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* أنماط للنصوص العربية */
.arabic-text {
  font-family: 'Cairo', sans-serif;
  text-align: right;
}

.english-text {
  font-family: 'Inter', sans-serif;
  text-align: left;
  direction: ltr;
}

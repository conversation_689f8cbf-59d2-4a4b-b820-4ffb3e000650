'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Star, Heart, ShoppingCart, Eye } from 'lucide-react';
import { Product } from '@/types';
import { formatPrice, hasDiscount, calculateDiscountPercentage } from '@/utils/helpers';
import { useCart } from '@/utils/cart-context';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import Card from '@/components/ui/Card';

interface ProductCardProps {
  product: Product;
  showQuickActions?: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({ 
  product, 
  showQuickActions = true 
}) => {
  const { addToCart } = useCart();

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addToCart(product, 1);
  };

  const handleQuickView = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // فتح نافذة العرض السريع
    console.log('Quick view:', product.id);
  };

  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // إضافة/إزالة من المفضلة
    console.log('Toggle favorite:', product.id);
  };

  return (
    <Card hover className="group relative overflow-hidden">
      <Link href={`/products/${product.id}`}>
        {/* صورة المنتج */}
        <div className="relative aspect-square overflow-hidden bg-gray-100">
          <Image
            src={product.images[0] || '/images/placeholder-product.jpg'}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
          
          {/* الشارات */}
          <div className="absolute top-2 right-2 flex flex-col gap-1">
            {product.isNew && (
              <Badge variant="success" size="sm">جديد</Badge>
            )}
            {product.isOnSale && hasDiscount(product) && (
              <Badge variant="error" size="sm">
                -{calculateDiscountPercentage(product.originalPrice!, product.price)}%
              </Badge>
            )}
            {!product.inStock && (
              <Badge variant="warning" size="sm">نفد المخزون</Badge>
            )}
          </div>

          {/* أزرار الإجراءات السريعة */}
          {showQuickActions && (
            <div className="absolute top-2 left-2 flex flex-col gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <button
                onClick={handleToggleFavorite}
                className="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md hover:bg-red-50 hover:text-red-500 transition-colors"
              >
                <Heart className="w-4 h-4" />
              </button>
              
              <button
                onClick={handleQuickView}
                className="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md hover:bg-primary-50 hover:text-primary-600 transition-colors"
              >
                <Eye className="w-4 h-4" />
              </button>
            </div>
          )}

          {/* زر إضافة للسلة */}
          {product.inStock && (
            <div className="absolute bottom-2 left-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <Button
                onClick={handleAddToCart}
                variant="primary"
                size="sm"
                fullWidth
                icon={<ShoppingCart className="w-4 h-4" />}
              >
                أضف للسلة
              </Button>
            </div>
          )}
        </div>

        {/* معلومات المنتج */}
        <div className="p-4">
          {/* اسم المنتج */}
          <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2 group-hover:text-primary-600 transition-colors">
            {product.name}
          </h3>

          {/* التقييم */}
          <div className="flex items-center gap-1 mb-2">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-4 h-4 ${
                    i < Math.floor(product.rating)
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <span className="text-sm text-gray-500">
              ({product.reviewsCount})
            </span>
          </div>

          {/* السعر */}
          <div className="flex items-center gap-2 mb-3">
            <span className="text-lg font-bold text-primary-600">
              {formatPrice(product.price)}
            </span>
            
            {hasDiscount(product) && (
              <span className="text-sm text-gray-500 line-through">
                {formatPrice(product.originalPrice!)}
              </span>
            )}
          </div>

          {/* الألوان المتاحة */}
          {product.colors && product.colors.length > 0 && (
            <div className="flex items-center gap-1 mb-2">
              <span className="text-xs text-gray-500 ml-2">الألوان:</span>
              <div className="flex gap-1">
                {product.colors.slice(0, 3).map((color, index) => (
                  <div
                    key={index}
                    className="w-4 h-4 rounded-full border border-gray-300"
                    style={{ backgroundColor: getColorCode(color) }}
                    title={color}
                  />
                ))}
                {product.colors.length > 3 && (
                  <span className="text-xs text-gray-500">+{product.colors.length - 3}</span>
                )}
              </div>
            </div>
          )}

          {/* الأحجام المتاحة */}
          {product.sizes && product.sizes.length > 0 && (
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-500 ml-2">الأحجام:</span>
              <div className="flex gap-1">
                {product.sizes.slice(0, 4).map((size, index) => (
                  <span
                    key={index}
                    className="text-xs bg-gray-100 px-1 py-0.5 rounded"
                  >
                    {size}
                  </span>
                ))}
                {product.sizes.length > 4 && (
                  <span className="text-xs text-gray-500">+{product.sizes.length - 4}</span>
                )}
              </div>
            </div>
          )}
        </div>
      </Link>
    </Card>
  );
};

// دالة مساعدة لتحويل اسم اللون إلى كود لوني
const getColorCode = (colorName: string): string => {
  const colorMap: { [key: string]: string } = {
    'أبيض': '#ffffff',
    'أسود': '#000000',
    'أحمر': '#ef4444',
    'أزرق': '#3b82f6',
    'أخضر': '#10b981',
    'أصفر': '#f59e0b',
    'بني': '#92400e',
    'رمادي': '#6b7280',
    'وردي': '#ec4899',
    'بنفسجي': '#8b5cf6',
    'ذهبي': '#f59e0b',
    'فضي': '#9ca3af'
  };
  
  return colorMap[colorName] || '#6b7280';
};

export default ProductCard;

@echo off
echo ========================================
echo       متجر جاسم الدليمي الإلكتروني
echo ========================================
echo.

echo تحقق من وجود Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت على النظام
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo Node.js موجود ✓
echo.

echo تثبيت التبعيات...
call npm install
if %errorlevel% neq 0 (
    echo خطأ في تثبيت التبعيات
    pause
    exit /b 1
)

echo التبعيات تم تثبيتها بنجاح ✓
echo.

echo بدء تشغيل المتجر...
echo المتجر سيكون متاح على: http://localhost:3000
echo.
echo اضغط Ctrl+C لإيقاف الخادم
echo.

call npm run dev

# متجر جاسم الدليمي الإلكتروني

متجر إلكتروني احترافي مبني بتقنيات حديثة لبيع المنتجات عبر الإنترنت.

## 🚀 المميزات

### 🎨 التصميم
- واجهة مستخدم عصرية وجذابة
- تصميم متجاوب مع جميع الشاشات
- دعم كامل للغة العربية مع RTL
- ألوان متناسقة (أزرق داكن وذهبي)
- خطوط عربية أنيقة (Cairo)

### 🛍️ مكونات المتجر
- **الصفحة الرئيسية**: عرض المنتجات المميزة والعروض
- **صفحات المنتجات**: تفاصيل كاملة مع صور متعددة
- **التصنيفات**: تنظيم المنتجات في فئات
- **سلة الشراء**: إدارة المنتجات المختارة
- **صفحة الدفع**: إتمام عملية الشراء
- **البحث والفلترة**: العثور على المنتجات بسهولة

### 🔧 الوظائف
- إدارة سلة الشراء مع Local Storage
- نظام تقييم المنتجات
- حساب الشحن والخصومات
- دعم أحجام وألوان متعددة
- إشعارات تفاعلية

## 🛠️ التقنيات المستخدمة

- **Frontend**: Next.js 14 مع TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **State Management**: React Context API
- **Fonts**: Google Fonts (Cairo)

## 📦 التثبيت والتشغيل

### المتطلبات
- Node.js 18+ 
- npm أو yarn

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd jasim-store
```

2. **تثبيت التبعيات**
```bash
npm install
# أو
yarn install
```

3. **تشغيل المشروع**
```bash
npm run dev
# أو
yarn dev
```

4. **فتح المتصفح**
افتح [http://localhost:3000](http://localhost:3000) لعرض المتجر.

## 📁 هيكل المشروع

```
jasim-store/
├── src/
│   ├── app/                 # صفحات Next.js
│   ├── components/          # مكونات React
│   │   ├── layout/         # مكونات التخطيط
│   │   ├── ui/             # مكونات واجهة المستخدم
│   │   ├── product/        # مكونات المنتجات
│   │   └── admin/          # مكونات الإدارة
│   ├── data/               # البيانات التجريبية
│   ├── types/              # أنواع TypeScript
│   └── utils/              # دوال مساعدة
├── public/                 # الملفات العامة
└── docs/                   # الوثائق
```

## 🎯 الصفحات المتاحة

- `/` - الصفحة الرئيسية
- `/products` - جميع المنتجات
- `/products/[id]` - تفاصيل المنتج
- `/categories` - الفئات
- `/cart` - سلة الشراء
- `/checkout` - إتمام الطلب
- `/about` - من نحن
- `/contact` - تواصل معنا

## 🔧 التخصيص

### تغيير الألوان
عدّل ملف `tailwind.config.js`:

```javascript
theme: {
  extend: {
    colors: {
      primary: {
        // ألوانك المخصصة
      },
      gold: {
        // ألوانك المخصصة
      }
    }
  }
}
```

### تغيير إعدادات المتجر
عدّل ملف `src/data/store-settings.ts`:

```typescript
export const storeSettings = {
  storeName: 'اسم متجرك',
  // باقي الإعدادات
}
```

### إضافة منتجات جديدة
عدّل ملف `src/data/products.ts`:

```typescript
export const products: Product[] = [
  // منتجاتك
]
```

## 🚀 النشر

### Vercel (مُوصى به)
```bash
npm run build
vercel --prod
```

### Netlify
```bash
npm run build
# ارفع مجلد .next
```

## 📱 المميزات المستقبلية

- [ ] لوحة تحكم إدارية
- [ ] نظام المستخدمين والمصادقة
- [ ] دفع إلكتروني حقيقي
- [ ] إشعارات البريد الإلكتروني
- [ ] تكامل مع قواعد البيانات
- [ ] API للموبايل
- [ ] نظام المراجعات والتقييمات
- [ ] برنامج الولاء والنقاط

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +964 770 123 4567
- **الموقع**: [jasimstore.com](https://jasimstore.com)

---

تم تطوير هذا المتجر بـ ❤️ باستخدام Next.js و Tailwind CSS

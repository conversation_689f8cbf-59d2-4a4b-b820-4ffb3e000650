'use client';

import React, { useState, useMemo } from 'react';
import { Filter, Grid, List, SlidersHorizontal } from 'lucide-react';
import Layout from '@/components/layout/Layout';
import ProductCard from '@/components/product/ProductCard';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { products, categories } from '@/data/products';
import { Product } from '@/types';
import { searchProducts, sortProducts, filterProductsByCategory, filterProductsByPriceRange, filterInStockProducts } from '@/utils/helpers';

const ProductsPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 10000]);
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'rating' | 'newest'>('newest');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showInStockOnly, setShowInStockOnly] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  // تطبيق الفلاتر والبحث
  const filteredProducts = useMemo(() => {
    let result = [...products];

    // البحث
    if (searchQuery.trim()) {
      result = searchProducts(result, searchQuery);
    }

    // فلترة حسب الفئة
    if (selectedCategory) {
      result = filterProductsByCategory(result, selectedCategory);
    }

    // فلترة حسب النطاق السعري
    result = filterProductsByPriceRange(result, priceRange[0], priceRange[1]);

    // فلترة المنتجات المتوفرة فقط
    if (showInStockOnly) {
      result = filterInStockProducts(result);
    }

    // الترتيب
    result = sortProducts(result, sortBy, sortOrder);

    return result;
  }, [searchQuery, selectedCategory, priceRange, showInStockOnly, sortBy, sortOrder]);

  const handlePriceRangeChange = (type: 'min' | 'max', value: string) => {
    const numValue = parseInt(value) || 0;
    if (type === 'min') {
      setPriceRange([numValue, priceRange[1]]);
    } else {
      setPriceRange([priceRange[0], numValue]);
    }
  };

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* رأس الصفحة */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">جميع المنتجات</h1>
          <p className="text-gray-600">اكتشف مجموعتنا الكاملة من المنتجات عالية الجودة</p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* الشريط الجانبي للفلاتر */}
          <div className={`lg:w-64 ${showFilters ? 'block' : 'hidden lg:block'}`}>
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-24">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">فلترة النتائج</h3>

              {/* البحث */}
              <div className="mb-6">
                <Input
                  placeholder="ابحث في المنتجات..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              {/* الفئات */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-3">الفئات</h4>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="category"
                      value=""
                      checked={selectedCategory === ''}
                      onChange={(e) => setSelectedCategory(e.target.value)}
                      className="ml-2"
                    />
                    جميع الفئات
                  </label>
                  {categories.map((category) => (
                    <label key={category.id} className="flex items-center">
                      <input
                        type="radio"
                        name="category"
                        value={category.id}
                        checked={selectedCategory === category.id}
                        onChange={(e) => setSelectedCategory(e.target.value)}
                        className="ml-2"
                      />
                      {category.name} ({category.productCount})
                    </label>
                  ))}
                </div>
              </div>

              {/* النطاق السعري */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-3">النطاق السعري</h4>
                <div className="space-y-3">
                  <Input
                    type="number"
                    placeholder="الحد الأدنى"
                    value={priceRange[0]}
                    onChange={(e) => handlePriceRangeChange('min', e.target.value)}
                  />
                  <Input
                    type="number"
                    placeholder="الحد الأعلى"
                    value={priceRange[1]}
                    onChange={(e) => handlePriceRangeChange('max', e.target.value)}
                  />
                </div>
              </div>

              {/* المتوفر فقط */}
              <div className="mb-6">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={showInStockOnly}
                    onChange={(e) => setShowInStockOnly(e.target.checked)}
                    className="ml-2"
                  />
                  المنتجات المتوفرة فقط
                </label>
              </div>

              {/* إعادة تعيين الفلاتر */}
              <Button
                variant="outline"
                fullWidth
                onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('');
                  setPriceRange([0, 10000]);
                  setShowInStockOnly(false);
                }}
              >
                إعادة تعيين
              </Button>
            </div>
          </div>

          {/* المحتوى الرئيسي */}
          <div className="flex-1">
            {/* شريط التحكم */}
            <div className="bg-white rounded-lg shadow-md p-4 mb-6">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="flex items-center gap-4">
                  <span className="text-gray-600">
                    {filteredProducts.length} منتج
                  </span>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowFilters(!showFilters)}
                    className="lg:hidden"
                    icon={<SlidersHorizontal className="w-4 h-4" />}
                  >
                    فلاتر
                  </Button>
                </div>

                <div className="flex items-center gap-4">
                  {/* الترتيب */}
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">ترتيب حسب:</span>
                    <select
                      value={`${sortBy}-${sortOrder}`}
                      onChange={(e) => {
                        const [sort, order] = e.target.value.split('-');
                        setSortBy(sort as any);
                        setSortOrder(order as any);
                      }}
                      className="border border-gray-300 rounded px-3 py-1 text-sm"
                    >
                      <option value="newest-desc">الأحدث</option>
                      <option value="name-asc">الاسم (أ-ي)</option>
                      <option value="name-desc">الاسم (ي-أ)</option>
                      <option value="price-asc">السعر (منخفض-عالي)</option>
                      <option value="price-desc">السعر (عالي-منخفض)</option>
                      <option value="rating-desc">التقييم</option>
                    </select>
                  </div>

                  {/* طريقة العرض */}
                  <div className="flex items-center border border-gray-300 rounded">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`p-2 ${viewMode === 'grid' ? 'bg-primary-600 text-white' : 'text-gray-600'}`}
                    >
                      <Grid className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`p-2 ${viewMode === 'list' ? 'bg-primary-600 text-white' : 'text-gray-600'}`}
                    >
                      <List className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* عرض المنتجات */}
            {filteredProducts.length > 0 ? (
              <div className={
                viewMode === 'grid'
                  ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                  : 'space-y-4'
              }>
                {filteredProducts.map((product) => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    showQuickActions={viewMode === 'grid'}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <Filter className="w-16 h-16 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد منتجات</h3>
                <p className="text-gray-600 mb-4">لم نجد أي منتجات تطابق معايير البحث</p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('');
                    setPriceRange([0, 10000]);
                    setShowInStockOnly(false);
                  }}
                >
                  إعادة تعيين الفلاتر
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ProductsPage;

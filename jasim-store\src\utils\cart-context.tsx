'use client';

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { CartItem, Product, CartContextType } from '@/types';
import { generateId } from './helpers';

// أنواع الإجراءات
type CartAction =
  | { type: 'ADD_TO_CART'; payload: { product: Product; quantity?: number; size?: string; color?: string } }
  | { type: 'REMOVE_FROM_CART'; payload: { productId: string } }
  | { type: 'UPDATE_QUANTITY'; payload: { productId: string; quantity: number } }
  | { type: 'CLEAR_CART' }
  | { type: 'LOAD_CART'; payload: { items: CartItem[] } };

// الحالة الأولية
const initialState: CartItem[] = [];

// مخفض السلة
const cartReducer = (state: CartItem[], action: CartAction): CartItem[] => {
  switch (action.type) {
    case 'ADD_TO_CART': {
      const { product, quantity = 1, size, color } = action.payload;
      
      // البحث عن المنتج الموجود بنفس المواصفات
      const existingItemIndex = state.findIndex(
        item => 
          item.productId === product.id &&
          item.selectedSize === size &&
          item.selectedColor === color
      );

      if (existingItemIndex > -1) {
        // تحديث الكمية إذا كان المنتج موجود
        const updatedItems = [...state];
        updatedItems[existingItemIndex].quantity += quantity;
        return updatedItems;
      } else {
        // إضافة منتج جديد
        const newItem: CartItem = {
          productId: product.id,
          product,
          quantity,
          selectedSize: size,
          selectedColor: color,
          addedAt: new Date().toISOString()
        };
        return [...state, newItem];
      }
    }

    case 'REMOVE_FROM_CART': {
      return state.filter(item => item.productId !== action.payload.productId);
    }

    case 'UPDATE_QUANTITY': {
      const { productId, quantity } = action.payload;
      if (quantity <= 0) {
        return state.filter(item => item.productId !== productId);
      }
      
      return state.map(item =>
        item.productId === productId
          ? { ...item, quantity }
          : item
      );
    }

    case 'CLEAR_CART': {
      return [];
    }

    case 'LOAD_CART': {
      return action.payload.items;
    }

    default:
      return state;
  }
};

// إنشاء السياق
const CartContext = createContext<CartContextType | undefined>(undefined);

// مزود السياق
export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [items, dispatch] = useReducer(cartReducer, initialState);

  // تحميل السلة من التخزين المحلي عند بدء التطبيق
  useEffect(() => {
    const savedCart = localStorage.getItem('jasim-store-cart');
    if (savedCart) {
      try {
        const parsedCart = JSON.parse(savedCart);
        dispatch({ type: 'LOAD_CART', payload: { items: parsedCart } });
      } catch (error) {
        console.error('خطأ في تحميل السلة:', error);
      }
    }
  }, []);

  // حفظ السلة في التخزين المحلي عند تغييرها
  useEffect(() => {
    localStorage.setItem('jasim-store-cart', JSON.stringify(items));
  }, [items]);

  // إضافة منتج إلى السلة
  const addToCart = (product: Product, quantity = 1, size?: string, color?: string) => {
    dispatch({
      type: 'ADD_TO_CART',
      payload: { product, quantity, size, color }
    });
  };

  // إزالة منتج من السلة
  const removeFromCart = (productId: string) => {
    dispatch({
      type: 'REMOVE_FROM_CART',
      payload: { productId }
    });
  };

  // تحديث كمية المنتج
  const updateQuantity = (productId: string, quantity: number) => {
    dispatch({
      type: 'UPDATE_QUANTITY',
      payload: { productId, quantity }
    });
  };

  // مسح السلة
  const clearCart = () => {
    dispatch({ type: 'CLEAR_CART' });
  };

  // حساب إجمالي عدد العناصر
  const getTotalItems = (): number => {
    return items.reduce((total, item) => total + item.quantity, 0);
  };

  // حساب إجمالي السعر
  const getTotalPrice = (): number => {
    return items.reduce((total, item) => {
      return total + (item.product.price * item.quantity);
    }, 0);
  };

  const value: CartContextType = {
    items,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getTotalItems,
    getTotalPrice
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

// خطاف لاستخدام السلة
export const useCart = (): CartContextType => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart يجب أن يُستخدم داخل CartProvider');
  }
  return context;
};

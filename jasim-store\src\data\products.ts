import { Product, Category } from '@/types';

export const categories: Category[] = [
  {
    id: '1',
    name: 'ملابس رجالية',
    nameEn: 'Men\'s Clothing',
    description: 'أحدث صيحات الموضة الرجالية',
    image: '/images/categories/mens-clothing.jpg',
    productCount: 25,
    subcategories: [
      { id: '1-1', name: 'قمصان', nameEn: 'Shirts', productCount: 8 },
      { id: '1-2', name: 'بناطيل', nameEn: 'Pants', productCount: 10 },
      { id: '1-3', name: 'أحذية', nameEn: 'Shoes', productCount: 7 }
    ]
  },
  {
    id: '2',
    name: 'ملابس نسائية',
    nameEn: 'Women\'s Clothing',
    description: 'أزياء نسائية عصرية وأنيقة',
    image: '/images/categories/womens-clothing.jpg',
    productCount: 30,
    subcategories: [
      { id: '2-1', name: 'فساتين', nameEn: 'Dresses', productCount: 12 },
      { id: '2-2', name: 'بلوزات', nameEn: 'Blouses', productCount: 10 },
      { id: '2-3', name: 'أحذية', nameEn: 'Shoes', productCount: 8 }
    ]
  },
  {
    id: '3',
    name: 'إلكترونيات',
    nameEn: 'Electronics',
    description: 'أحدث الأجهزة الإلكترونية',
    image: '/images/categories/electronics.jpg',
    productCount: 20,
    subcategories: [
      { id: '3-1', name: 'هواتف ذكية', nameEn: 'Smartphones', productCount: 8 },
      { id: '3-2', name: 'لابتوب', nameEn: 'Laptops', productCount: 6 },
      { id: '3-3', name: 'إكسسوارات', nameEn: 'Accessories', productCount: 6 }
    ]
  },
  {
    id: '4',
    name: 'أدوات منزلية',
    nameEn: 'Home & Kitchen',
    description: 'كل ما تحتاجه للمنزل',
    image: '/images/categories/home-kitchen.jpg',
    productCount: 15,
    subcategories: [
      { id: '4-1', name: 'أدوات المطبخ', nameEn: 'Kitchen Tools', productCount: 8 },
      { id: '4-2', name: 'ديكور', nameEn: 'Decor', productCount: 7 }
    ]
  }
];

export const products: Product[] = [
  {
    id: '1',
    name: 'قميص قطني أنيق',
    nameEn: 'Elegant Cotton Shirt',
    description: 'قميص قطني عالي الجودة، مناسب للمناسبات الرسمية والكاجوال',
    descriptionEn: 'High-quality cotton shirt, suitable for formal and casual occasions',
    price: 120,
    originalPrice: 150,
    discount: 20,
    images: [
      '/images/products/shirt-1-1.jpg',
      '/images/products/shirt-1-2.jpg',
      '/images/products/shirt-1-3.jpg'
    ],
    category: '1',
    subcategory: '1-1',
    inStock: true,
    stockQuantity: 50,
    rating: 4.5,
    reviewsCount: 23,
    sizes: ['S', 'M', 'L', 'XL', 'XXL'],
    colors: ['أبيض', 'أزرق', 'أسود'],
    tags: ['قطني', 'رسمي', 'كاجوال'],
    featured: true,
    isNew: false,
    isOnSale: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T15:30:00Z'
  },
  {
    id: '2',
    name: 'فستان سهرة أنيق',
    nameEn: 'Elegant Evening Dress',
    description: 'فستان سهرة راقي مصمم خصيصاً للمناسبات الخاصة',
    descriptionEn: 'Elegant evening dress designed specifically for special occasions',
    price: 350,
    originalPrice: 400,
    discount: 12.5,
    images: [
      '/images/products/dress-1-1.jpg',
      '/images/products/dress-1-2.jpg'
    ],
    category: '2',
    subcategory: '2-1',
    inStock: true,
    stockQuantity: 15,
    rating: 4.8,
    reviewsCount: 12,
    sizes: ['S', 'M', 'L', 'XL'],
    colors: ['أسود', 'أحمر', 'أزرق داكن'],
    tags: ['سهرة', 'أنيق', 'مناسبات'],
    featured: true,
    isNew: true,
    isOnSale: true,
    createdAt: '2024-01-20T14:00:00Z',
    updatedAt: '2024-01-22T09:15:00Z'
  },
  {
    id: '3',
    name: 'هاتف ذكي متطور',
    nameEn: 'Advanced Smartphone',
    description: 'هاتف ذكي بأحدث التقنيات وكاميرا عالية الدقة',
    descriptionEn: 'Smartphone with latest technology and high-resolution camera',
    price: 2500,
    images: [
      '/images/products/phone-1-1.jpg',
      '/images/products/phone-1-2.jpg',
      '/images/products/phone-1-3.jpg'
    ],
    category: '3',
    subcategory: '3-1',
    inStock: true,
    stockQuantity: 25,
    rating: 4.7,
    reviewsCount: 45,
    colors: ['أسود', 'أبيض', 'ذهبي'],
    tags: ['هاتف', 'ذكي', 'كاميرا'],
    featured: true,
    isNew: true,
    isOnSale: false,
    createdAt: '2024-01-25T11:00:00Z',
    updatedAt: '2024-01-25T11:00:00Z'
  }
];

// منتجات مميزة
export const featuredProducts = products.filter(product => product.featured);

// منتجات جديدة
export const newProducts = products.filter(product => product.isNew);

// منتجات في التخفيضات
export const saleProducts = products.filter(product => product.isOnSale);

// الأكثر مبيعاً (حسب عدد التقييمات)
export const bestSellingProducts = products
  .sort((a, b) => b.reviewsCount - a.reviewsCount)
  .slice(0, 8);

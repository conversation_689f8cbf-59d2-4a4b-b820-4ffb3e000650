<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متجر جاسم الدليمي - معاينة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        gold: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                    },
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            scroll-behavior: smooth;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
        }

        /* تأثيرات الانتقال السلس */
        section {
            transition: transform 0.3s ease-in-out;
        }

        /* تأثيرات الأزرار */
        button {
            transition: all 0.3s ease;
        }

        button:hover {
            transform: translateY(-2px);
        }

        /* تأثيرات البطاقات */
        .product-card {
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* تأثير النبض للشارات */
        .cart-badge {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        /* تأثير التمرير السلس */
        html {
            scroll-behavior: smooth;
        }

        /* تأثيرات الإشعارات */
        .notification {
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* تحسين الاستجابة */
        @media (max-width: 768px) {
            .hero-text {
                text-align: center;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <!-- Top Bar -->
        <div class="bg-primary-600 text-white py-2">
            <div class="max-w-7xl mx-auto px-4">
                <div class="flex justify-between items-center text-sm">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <span>📞 +964 770 123 4567</span>
                        <span>✉️ <EMAIL></span>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <span>🚚 شحن مجاني للطلبات أكثر من 100,000 د.ع</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Header -->
        <div class="max-w-7xl mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <!-- Logo -->
                <div class="flex items-center space-x-2 space-x-reverse">
                    <div class="w-10 h-10 bg-gradient-to-r from-primary-600 to-gold-500 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-xl">ج</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">متجر جاسم الدليمي</h1>
                        <p class="text-sm text-gray-500">متجرك الإلكتروني المتميز</p>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="hidden md:flex flex-1 max-w-lg mx-8">
                    <div class="w-full relative">
                        <input type="text" placeholder="ابحث عن المنتجات..."
                               class="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                        <button class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">
                            🔍
                        </button>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button class="p-2 text-gray-600 hover:text-primary-600">❤️</button>
                    <button class="p-2 text-gray-600 hover:text-primary-600">👤</button>
                    <button class="relative p-2 text-gray-600 hover:text-primary-600">
                        🛒
                        <span class="cart-badge absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="bg-gray-50 border-t">
            <div class="max-w-7xl mx-auto px-4">
                <div class="flex items-center justify-center space-x-8 space-x-reverse py-3">
                    <a href="#" class="text-gray-700 hover:text-primary-600 font-medium">الرئيسية</a>
                    <a href="#" class="text-gray-700 hover:text-primary-600 font-medium">المنتجات</a>
                    <a href="#" class="text-gray-700 hover:text-primary-600 font-medium">الفئات</a>
                    <a href="#" class="text-gray-700 hover:text-primary-600 font-medium">العروض</a>
                    <a href="#" class="text-gray-700 hover:text-primary-600 font-medium">من نحن</a>
                    <a href="#" class="text-gray-700 hover:text-primary-600 font-medium">تواصل معنا</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="gradient-bg text-white overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 py-20">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div class="text-center lg:text-right">
                    <h1 class="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
                        مرحباً بك في
                        <span class="block text-gold-400">متجر جاسم الدليمي</span>
                    </h1>
                    <p class="text-xl lg:text-2xl mb-8 text-gray-200 leading-relaxed">
                        اكتشف أفضل المنتجات بجودة عالية وأسعار منافسة
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                        <button onclick="scrollToProducts()" class="bg-gold-500 hover:bg-gold-600 text-white font-medium py-3 px-6 rounded-lg text-lg transition-all duration-300 hover:scale-105">
                            تسوق الآن ←
                        </button>
                        <button onclick="scrollToCategories()" class="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium py-3 px-6 rounded-lg text-lg transition-all duration-300">
                            استكشف الفئات
                        </button>
                    </div>
                </div>

                <div class="relative">
                    <div class="relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl">
                        <img src="https://via.placeholder.com/600x500/1e40af/ffffff?text=متجر+جاسم+الدليمي"
                             alt="متجر جاسم الدليمي" class="w-full h-full object-cover">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section id="categories" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">تسوق حسب الفئة</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    اكتشف مجموعتنا المتنوعة من المنتجات المصنفة بعناية لتسهيل تجربة التسوق
                </p>
            </div>

            <div class="grid grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                    <div class="h-48 bg-primary-100">
                        <img src="https://via.placeholder.com/300x200/1e40af/ffffff?text=ملابس+رجالية"
                             alt="ملابس رجالية" class="w-full h-full object-cover">
                    </div>
                    <div class="p-4 text-center">
                        <h3 class="font-semibold text-lg text-gray-900 mb-2">ملابس رجالية</h3>
                        <p class="text-sm text-gray-500 mb-3">أحدث صيحات الموضة الرجالية</p>
                        <span class="text-primary-600 font-medium">25 منتج</span>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                    <div class="h-48 bg-pink-100">
                        <img src="https://via.placeholder.com/300x200/ec4899/ffffff?text=ملابس+نسائية"
                             alt="ملابس نسائية" class="w-full h-full object-cover">
                    </div>
                    <div class="p-4 text-center">
                        <h3 class="font-semibold text-lg text-gray-900 mb-2">ملابس نسائية</h3>
                        <p class="text-sm text-gray-500 mb-3">أزياء نسائية عصرية وأنيقة</p>
                        <span class="text-primary-600 font-medium">30 منتج</span>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                    <div class="h-48 bg-gray-100">
                        <img src="https://via.placeholder.com/300x200/374151/ffffff?text=إلكترونيات"
                             alt="إلكترونيات" class="w-full h-full object-cover">
                    </div>
                    <div class="p-4 text-center">
                        <h3 class="font-semibold text-lg text-gray-900 mb-2">إلكترونيات</h3>
                        <p class="text-sm text-gray-500 mb-3">أحدث الأجهزة الإلكترونية</p>
                        <span class="text-primary-600 font-medium">20 منتج</span>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                    <div class="h-48 bg-green-100">
                        <img src="https://via.placeholder.com/300x200/10b981/ffffff?text=أدوات+منزلية"
                             alt="أدوات منزلية" class="w-full h-full object-cover">
                    </div>
                    <div class="p-4 text-center">
                        <h3 class="font-semibold text-lg text-gray-900 mb-2">أدوات منزلية</h3>
                        <p class="text-sm text-gray-500 mb-3">كل ما تحتاجه للمنزل</p>
                        <span class="text-primary-600 font-medium">15 منتج</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products -->
    <section id="products" class="py-16">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center mb-8">
                <div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">المنتجات المميزة</h2>
                    <p class="text-gray-600">أفضل منتجاتنا المختارة بعناية</p>
                </div>
                <button class="border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-2 px-4 rounded-lg">
                    عرض الكل ←
                </button>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Product Card 1 -->
                <div class="product-card bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="relative">
                        <img src="https://via.placeholder.com/400x400/1e40af/ffffff?text=قميص+قطني"
                             alt="قميص قطني" class="w-full h-64 object-cover">
                        <div class="absolute top-2 right-2">
                            <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">-20%</span>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-1">قميص قطني أنيق</h3>
                        <div class="flex items-center mb-2">
                            <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                            <span class="text-sm text-gray-500 mr-1">(23)</span>
                        </div>
                        <div class="flex items-center gap-2 mb-3">
                            <span class="text-lg font-bold text-primary-600">120 د.ع</span>
                            <span class="text-sm text-gray-500 line-through">150 د.ع</span>
                        </div>
                        <button class="w-full bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700">
                            أضف للسلة
                        </button>
                    </div>
                </div>

                <!-- Product Card 2 -->
                <div class="product-card bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="relative">
                        <img src="https://via.placeholder.com/400x400/ec4899/ffffff?text=فستان+سهرة"
                             alt="فستان سهرة" class="w-full h-64 object-cover">
                        <div class="absolute top-2 right-2">
                            <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">جديد</span>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-1">فستان سهرة أنيق</h3>
                        <div class="flex items-center mb-2">
                            <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                            <span class="text-sm text-gray-500 mr-1">(12)</span>
                        </div>
                        <div class="flex items-center gap-2 mb-3">
                            <span class="text-lg font-bold text-primary-600">350 د.ع</span>
                            <span class="text-sm text-gray-500 line-through">400 د.ع</span>
                        </div>
                        <button class="w-full bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700">
                            أضف للسلة
                        </button>
                    </div>
                </div>

                <!-- Product Card 3 -->
                <div class="product-card bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="relative">
                        <img src="https://via.placeholder.com/400x400/374151/ffffff?text=هاتف+ذكي"
                             alt="هاتف ذكي" class="w-full h-64 object-cover">
                        <div class="absolute top-2 right-2">
                            <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">مميز</span>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-1">هاتف ذكي متطور</h3>
                        <div class="flex items-center mb-2">
                            <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                            <span class="text-sm text-gray-500 mr-1">(45)</span>
                        </div>
                        <div class="flex items-center gap-2 mb-3">
                            <span class="text-lg font-bold text-primary-600">2,500 د.ع</span>
                        </div>
                        <button class="w-full bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700">
                            أضف للسلة
                        </button>
                    </div>
                </div>

                <!-- Product Card 4 -->
                <div class="product-card bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="relative">
                        <img src="https://via.placeholder.com/400x400/10b981/ffffff?text=أدوات+مطبخ"
                             alt="أدوات مطبخ" class="w-full h-64 object-cover">
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-1">أدوات مطبخ عملية</h3>
                        <div class="flex items-center mb-2">
                            <span class="text-yellow-400">⭐⭐⭐⭐</span>
                            <span class="text-sm text-gray-500 mr-1">(18)</span>
                        </div>
                        <div class="flex items-center gap-2 mb-3">
                            <span class="text-lg font-bold text-primary-600">85 د.ع</span>
                        </div>
                        <button class="w-full bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700">
                            أضف للسلة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-primary-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">لماذا تختارنا؟</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    نقدم لك أفضل تجربة تسوق مع خدمات متميزة وجودة عالية
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl">
                        🚚
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">شحن سريع</h3>
                    <p class="text-gray-600">توصيل مجاني للطلبات أكثر من 100,000 د.ع</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl">
                        🛡️
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">دفع آمن</h3>
                    <p class="text-gray-600">حماية كاملة لبياناتك المالية</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl">
                        🎧
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">دعم 24/7</h3>
                    <p class="text-gray-600">خدمة عملاء متاحة على مدار الساعة</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl">
                        ↩️
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">إرجاع مجاني</h3>
                    <p class="text-gray-600">إمكانية الإرجاع خلال 30 يوم</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Store Info -->
                <div>
                    <div class="flex items-center space-x-2 space-x-reverse mb-4">
                        <div class="w-10 h-10 bg-gradient-to-r from-primary-600 to-gold-500 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-xl">ج</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">متجر جاسم الدليمي</h3>
                            <p class="text-gray-400 text-sm">متجرك الإلكتروني المتميز</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-6 leading-relaxed">
                        متجرك الإلكتروني المتخصص في بيع أفضل المنتجات بجودة عالية وأسعار منافسة
                    </p>
                    <div class="flex space-x-4 space-x-reverse">
                        <a href="#" class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700">📘</a>
                        <a href="#" class="w-10 h-10 bg-pink-600 rounded-lg flex items-center justify-center hover:bg-pink-700">📷</a>
                        <a href="#" class="w-10 h-10 bg-blue-400 rounded-lg flex items-center justify-center hover:bg-blue-500">🐦</a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gold-400">روابط سريعة</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-white">الرئيسية</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">المنتجات</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">الفئات</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">العروض</a></li>
                    </ul>
                </div>

                <!-- Customer Service -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gold-400">خدمة العملاء</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-white">تواصل معنا</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">الأسئلة الشائعة</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">سياسة الإرجاع</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">الشحن والتوصيل</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gold-400">معلومات الاتصال</h4>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <span class="text-primary-400">📞</span>
                            <span>+964 770 123 4567</span>
                        </div>
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <span class="text-primary-400">✉️</span>
                            <span><EMAIL></span>
                        </div>
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <span class="text-primary-400">📍</span>
                            <span>بغداد، العراق</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Copyright -->
        <div class="bg-gray-800 py-4">
            <div class="max-w-7xl mx-auto px-4">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm">
                        © 2024 متجر جاسم الدليمي. جميع الحقوق محفوظة.
                    </p>
                    <div class="flex items-center space-x-4 space-x-reverse mt-2 md:mt-0">
                        <span class="text-gray-400 text-sm">مدعوم بـ</span>
                        <span class="text-gold-400 font-medium">Next.js & Tailwind CSS</span>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // وظائف التنقل السلس
        function scrollToProducts() {
            const productsSection = document.getElementById('products');
            productsSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // إضافة تأثير بصري
            productsSection.style.transform = 'scale(1.02)';
            setTimeout(() => {
                productsSection.style.transform = 'scale(1)';
            }, 300);
        }

        function scrollToCategories() {
            const categoriesSection = document.getElementById('categories');
            categoriesSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // إضافة تأثير بصري
            categoriesSection.style.transform = 'scale(1.02)';
            setTimeout(() => {
                categoriesSection.style.transform = 'scale(1)';
            }, 300);
        }

        // إضافة تفاعل مع أزرار إضافة للسلة
        document.addEventListener('DOMContentLoaded', function() {
            // عداد السلة
            let cartCount = 3;

            // البحث عن جميع أزرار "أضف للسلة"
            const addToCartButtons = document.querySelectorAll('button');
            addToCartButtons.forEach(button => {
                if (button.textContent.includes('أضف للسلة')) {
                    button.addEventListener('click', function() {
                        // زيادة عداد السلة
                        cartCount++;
                        const cartBadge = document.querySelector('.bg-red-500');
                        cartBadge.textContent = cartCount;

                        // تأثير بصري للزر
                        button.style.transform = 'scale(0.95)';
                        button.textContent = 'تم الإضافة ✓';
                        button.style.backgroundColor = '#10b981';

                        setTimeout(() => {
                            button.style.transform = 'scale(1)';
                            button.textContent = 'أضف للسلة';
                            button.style.backgroundColor = '';
                        }, 1500);

                        // إشعار
                        showNotification('تم إضافة المنتج إلى السلة! 🛒');
                    });
                }
            });

            // وظيفة عرض الإشعارات
            function showNotification(message) {
                const notification = document.createElement('div');
                notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.style.transform = 'translateX(0)';
                }, 100);

                setTimeout(() => {
                    notification.style.transform = 'translateX(full)';
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 3000);
            }

            // تحسين تجربة التنقل في الرأس
            const navLinks = document.querySelectorAll('nav a');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.textContent === 'المنتجات') {
                        e.preventDefault();
                        scrollToProducts();
                    } else if (this.textContent === 'الفئات') {
                        e.preventDefault();
                        scrollToCategories();
                    }
                });
            });

            // تأثيرات hover للبطاقات
            const productCards = document.querySelectorAll('.bg-white.rounded-lg.shadow-md');
            productCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '';
                });
            });
        });
    </script>
</body>
</html>
